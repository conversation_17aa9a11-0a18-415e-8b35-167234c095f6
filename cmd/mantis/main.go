package main

import (
	"context"
	"fmt"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/cache"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/elasticsearch"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/pubsub"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/s3store"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver/tekton/tasks"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/expand"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/k8s"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/third_party"

	"github.com/gorilla/mux"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/cron"
	cubeContext "git.zhonganinfo.com/zainfo/shiplib/pkgs/context"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/cors"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/healthcheck"
	httplib "git.zhonganinfo.com/zainfo/shiplib/pkgs/http"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/audit"
	notify "git.zhonganinfo.com/zainfo/shiplib/pkgs/message/cube"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common"
	commonRouter "git.zhonganinfo.com/zainfo/cube-mantis/app/common/router"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury"
	mercuryRouter "git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/router"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune"
	neptuneRouter "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/router"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus"
	venusRouter "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/router"
)

type ModuleConfig struct {
	name   string
	enable bool
	init   func()
	router func(context.Context, *configs.Configs, *mux.Router) *mux.Router
}

// loadModules 加载模块配置
func loadModules() []ModuleConfig {
	return []ModuleConfig{
		{
			name:   "common",
			enable: true,
			init:   common.Init,
			router: commonRouter.InstallRouter,
		},
		{
			name:   "venus",
			enable: configs.Config.Modules.Venus.Enable,
			init:   venus.Init,
			router: venusRouter.InstallRouter,
		},
		{
			name:   "mercury",
			enable: configs.Config.Modules.Mercury.Enable,
			init:   mercury.Init,
			router: mercuryRouter.InstallRouter,
		},
		{
			name:   "neptune",
			enable: configs.Config.Modules.Neptune.Enable,
			init:   neptune.Init,
			router: neptuneRouter.InstallRouter,
		},
	}
}

// initializeModules 模块初始化并配置路由
func initializeModules(ctx context.Context, router *mux.Router, modules []ModuleConfig) *mux.Router {
	for _, module := range modules {
		if !module.enable {
			continue
		}
		module.init()
		router = module.router(ctx, configs.Config, router)
	}
	return router
}

func initializePipeline(modules []ModuleConfig) {
	// 检查是否需要初始化pipeline相关组件
	needInit := false
	for _, module := range modules {
		if module.enable && (module.name == "neptune" || module.name == "venus") {
			needInit = true
			break
		}
	}

	if !needInit {
		return
	}

	k8s.Init()
	tasks.Init()
	// controllermanager.Init()
}

func main() {
	ctx := cubeContext.WithSignal(context.Background())

	// 初始化基础设施
	configs.Init()
	logger.Init()
	gormx.Init()
	cache.Init()
	elasticsearch.Init()
	snowflake.Init()
	s3store.Init()
	goroutine.Init()
	third_party.Init()
	notify.Init(configs.Config.Notification)
	audit.Init(audit.Config{
		TimeOut:  configs.Config.AuditLog.Timeout,
		AuditURL: configs.Config.AuditLog.Endpoint,
	})
	expand.Init()

	// 获取模块配置
	modules := loadModules()

	// 初始化任务流水线
	initializePipeline(modules)

	// 初始化消息中心
	pubsub.Init()

	// 初始化模块并配置路由
	router := mux.NewRouter().UseEncodedPath()
	router = initializeModules(ctx, router, modules)

	// 初始化cron
	cron.Init()

	// 配置中间件和启动服务
	handler := cors.CORS(configs.Config.Cors)(healthcheck.Handler(healthcheck.WithContext(ctx))(router))
	errCh := httplib.MultiListenAndServerTLS(":"+configs.Config.App.Port, "", handler, nil)

	select {
	case err := <-errCh:
		fmt.Println("Start server err:", err)
	case <-ctx.Done():
		fmt.Println("Stopping server")
		cron.ShutDown()
	}
}
