package pubsub

import (
	"context"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/cache"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	cubeasynq "git.zhonganinfo.com/zainfo/shiplib/pkgs/pubsub/asynq"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/pubsub/factory"
)

var Client *factory.Client

func Init() {
	// 从初始化的redis客户端中获取信息
	options := cache.GetRedisClient().Options()
	logger.Logger.Debugf("redis初始化信息:%+v", *options)
	// 生产客户端
	client, err := factory.NewClient(factory.Config{
		Driver: factory.DriverAsynq,
		Asynq: cubeasynq.Config{
			Addr:        options.Addr,
			Username:    options.Username,
			Password:    options.Password,
			Concurrency: 1,
			Queues: map[string]int{
				"pipeline": 6, // 流水线队列中的任务将被处理 60% 的时间
				"web":      2, //  web队列中的任务将被处理 30% 的时间
				"default":  1, // 默认队列中的任务将被处理 10% 的时间
				"ui":       1,
			},
			ShutdownTimeoutSecond: 10,
			DB:                    options.DB,
			Logger:                logger.Logger,
		},
	})
	if err != nil {
		logger.Logger.Panicf("初始化消息队列错误, err=%s", err.Error())
	}
	Client = client
	logger.Logger.Info("启动生产者客户端")
	go func() {
		if err = Client.Start(context.Background()); err != nil {
			logger.Logger.Panicf("初始化消息队列错误, err=%s", err.Error())
		}
	}()
}
