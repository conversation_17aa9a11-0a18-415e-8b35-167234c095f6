package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/notification"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/types"
)

type NoticeService struct{}

const (
	// 通知模板常量
	uiTitle     = `UI 测试报告: %s`
	uiContent   = "%s\n执行时间: %s \n通过率: %s\n耗　时: %d ms\n成功数: %d\n失败数: %d\n总　计: %d\n\n报告详情: %s"
	uiReportUrl = "%s/project/%s/ui-test/history-report/detail/%s"
)

// PlanNotice 按计划规则通知
func (s NoticeService) PlanNotice(reportId string) {
	if strings.TrimSpace(reportId) == "" {
		logger.Logger.Errorf("PlanNotice...reportId不能为空")
		return
	}

	goroutine.Run(func() {
		s.executePlanNotice(reportId)
	})
}

// executePlanNotice 执行计划通知的核心逻辑
func (s NoticeService) executePlanNotice(reportId string) {
	ctx := &commoncontext.MantisContext{Context: context.Background()}
	start := time.Now()
	logger.Logger.Infof("PlanNotice...开始执行计划通知,reportId=%s", reportId)

	// 获取报告信息
	report := &models.UiReport{}
	report.Id = reportId
	report.IsDeleted = false
	err := gormx.SelectOneByConditionReturnError(ctx, report)
	if err != nil {
		logger.Logger.Errorf("PlanNotice...获取报告失败: %s", err)
		return
	}

	// 检查是否需要跳过通知
	if s.shouldSkipNotification(report) {
		logger.Logger.Infof("PlanNotice...报告类型是 debug 或者 cloud, 不发送通知")
		return
	}

	// 获取计划信息
	plan := &models.UiPlan{}
	plan.Id = report.RelationId
	plan.IsDeleted = false
	err = gormx.SelectOneByConditionReturnError(ctx, plan)
	if err != nil {
		logger.Logger.Errorf("PlanNotice...获取计划 planId=%s 失败: %s", report.RelationId, err)
		return
	}

	// 构建通知内容
	noticeContent := s.buildNoticeContent(report, plan, reportId)

	// 处理通知发送
	webhookResults := s.processNotifications(report, plan, noticeContent)

	// 处理报告的webhook
	reportWebhookResults := s.processReportWebhooks(report, noticeContent)
	webhookResults = append(webhookResults, reportWebhookResults...)

	// 更新报告的webhook结果
	if err := s.updateReportWebhookResults(ctx, report, webhookResults, plan.Id); err != nil {
		logger.Logger.Errorf("PlanNotice...发送通知失败:planId=%s, %s", plan.Id, err)
	}

	logger.Logger.Infof("PlanNotice...执行计划通知结束,planId=%s,reportId=%s,cost=%d ms", plan.Id, reportId, time.Since(start).Milliseconds())
}

func timeToStr(millis int64) string {
	// 将毫秒时间戳转换为time.Time
	t := time.Unix(0, millis*int64(time.Millisecond))
	// 格式化为指定的字符串格式
	formatted := t.Format("2006:01:02 15:04:05")
	return formatted
}

// floatToPercentage 小数转百分比
func floatToPercentage(s string) string {
	if strings.TrimSpace(s) == "" {
		return "0.00%"
	}

	f, err := strconv.ParseFloat(s, 64)
	if err != nil {
		logger.Logger.Warnf("解析浮点数失败: %s, err: %v", s, err)
		return "0.00%"
	}

	return fmt.Sprintf("%.2f%%", f*100)
}

func dialWebhook(webhook string, report models.UiReport, reportUrl, appId string, success bool, msg string) *models.WebhookResult {
	webhookRes := &models.WebhookResult{
		Url: webhook,
	}

	// 构建请求体
	requestBody := buildWebhookRequestBody(report, reportUrl, appId, success, msg)
	webhookRes.Request.Body = requestBody
	webhookRes.Request.Method = http.MethodPost

	// 发送HTTP请求
	resp, err := sendWebhookRequest(webhook, requestBody)
	if err != nil {
		logger.Logger.Warnf("UI测试执行回调失败. 回调地址:%s. err=%s", webhook, err.Error())
		return webhookRes
	}
	defer resp.Body.Close()

	// 处理响应
	webhookRes.Response.StatusCode = resp.StatusCode
	if resp.StatusCode >= 300 {
		logger.Logger.Warnf("ui测试执行回调失败. 回调地址:%s. status code=%d", webhook, resp.StatusCode)
	}

	// 解析响应体
	responseBody, err := parseWebhookResponse(resp)
	if err != nil {
		logger.Logger.Warnf("ui测试执行回调失败. 回调地址:%s. err=%s", webhook, err.Error())
		return webhookRes
	}

	webhookRes.Response.Body = responseBody
	return webhookRes
}

// buildWebhookRequestBody 构建webhook请求体
func buildWebhookRequestBody(report models.UiReport, reportUrl, appId string, success bool, msg string) map[string]any {
	return map[string]any{
		"success":          success,
		"msg":              msg,
		"report_id":        report.Id,
		"app_id":           appId,
		"plan_id":          report.RelationId,
		"env":              report.Env,
		"duration":         report.Duration,
		"start_time":       report.ExecStartTime,
		"pass_rate":        report.PassRate,
		"case_pass_count":  report.CasePassCount,
		"case_fail_count":  report.CaseFailCount,
		"case_total_count": report.CaseTotalCount,
		"report_url":       reportUrl,
	}
}

// sendWebhookRequest 发送webhook HTTP请求
func sendWebhookRequest(webhook string, requestBody map[string]any) (*http.Response, error) {
	body, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("marshal request body failed: %w", err)
	}

	return http.Post(webhook, "application/json", bytes.NewBuffer(body))
}

// parseWebhookResponse 解析webhook响应
func parseWebhookResponse(resp *http.Response) (map[string]any, error) {
	responseBody := make(map[string]any)

	respJson, err := io.ReadAll(resp.Body)
	if err != nil {
		return responseBody, fmt.Errorf("read response body failed: %w", err)
	}

	if len(respJson) > 0 {
		if err := json.Unmarshal(respJson, &responseBody); err != nil {
			return responseBody, fmt.Errorf("unmarshal response body failed: %w", err)
		}
	}

	return responseBody, nil
}

// shouldSkipNotification 检查是否应该跳过通知
func (s NoticeService) shouldSkipNotification(report *models.UiReport) bool {
	return report.ExecType == string(types.Debug) || report.ExecType == string(types.Cloud)
}

// NoticeContent 通知内容结构体
type NoticeContent struct {
	ReportUrl string
	Title     string
	Content   string
	Success   bool
	Message   string
}

// buildNoticeContent 构建通知内容
func (s NoticeService) buildNoticeContent(report *models.UiReport, plan *models.UiPlan, reportId string) *NoticeContent {
	reportUrl := fmt.Sprintf(uiReportUrl, configs.Config.Domain.Cube, plan.SpaceId, reportId)
	title := fmt.Sprintf(uiTitle, plan.Name)
	success := report.Status == constants.ReportStatusSuccess

	var content, msg string
	if success {
		msg = "执行成功"
		content = fmt.Sprintf(uiContent, title, timeToStr(report.ExecStartTime), floatToPercentage(report.PassRate),
			report.Duration, report.CasePassCount, report.CaseFailCount, report.CaseTotalCount, reportUrl)
	} else {
		msg = "执行机错误"
		content = fmt.Sprintf("%s\n%s", title, "执行失败")
	}

	return &NoticeContent{
		ReportUrl: reportUrl,
		Title:     title,
		Content:   content,
		Success:   success,
		Message:   msg,
	}
}

// processNotifications 处理通知发送
func (s NoticeService) processNotifications(report *models.UiReport, plan *models.UiPlan, noticeContent *NoticeContent) models.WebhookResultSlice {
	webhookResults := make(models.WebhookResultSlice, 0)

	// 检查是否需要发送通知
	shouldSendNotification := s.shouldSendNotification(report, plan)
	if !shouldSendNotification {
		return webhookResults
	}

	// 发送通知
	if err := notification.Send(plan.NoticeUsers, noticeContent.Title, noticeContent.Content, plan.NoticeType, ""); err != nil {
		logger.Logger.Errorf("PlanNotice...发送通知失败:planId=%s, %s", plan.Id, err)
	}

	// 处理计划的webhooks
	for _, webhook := range plan.Webhooks {
		if webhookResult := s.callWebhook(webhook, report, noticeContent, plan.AppId); webhookResult != nil {
			webhookResults = append(webhookResults, *webhookResult)
		}
	}

	return webhookResults
}

// shouldSendNotification 判断是否应该发送通知
func (s NoticeService) shouldSendNotification(report *models.UiReport, plan *models.UiPlan) bool {
	return (report.Status == constants.ReportStatusFail && plan.NoticeRule == types.NotificationFail) ||
		(plan.NoticeRule == types.NotificationAlways)
}

// processReportWebhooks 处理报告的webhooks
func (s NoticeService) processReportWebhooks(report *models.UiReport, noticeContent *NoticeContent) models.WebhookResultSlice {
	webhookResults := make(models.WebhookResultSlice, 0)

	for _, webhook := range report.Webhooks {
		if webhookResult := s.callWebhook(webhook, report, noticeContent, ""); webhookResult != nil {
			webhookResults = append(webhookResults, *webhookResult)
		}
	}

	return webhookResults
}

// callWebhook 调用webhook的统一方法
func (s NoticeService) callWebhook(webhook string, report *models.UiReport, noticeContent *NoticeContent, appId string) *models.WebhookResult {
	return dialWebhook(webhook, *report, noticeContent.ReportUrl, appId, noticeContent.Success, noticeContent.Message)
}

// updateReportWebhookResults 更新报告的webhook结果
func (s NoticeService) updateReportWebhookResults(ctx *commoncontext.MantisContext, report *models.UiReport, webhookResults models.WebhookResultSlice, planId string) error {
	report.WebhookResults = webhookResults
	_, err := gormx.UpdateOneByConditionReturnError(ctx, report)
	return err
}
