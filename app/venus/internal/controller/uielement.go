// CRUD methods generated by pb-api-generator.

package controller

import (
	"errors"
	"fmt"

	dto "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto/generated"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/expand"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"gorm.io/gorm"
)

type UiElementIdRequest struct {
	Id string `path:"id"`
}

type UiElementController struct {
	*controller.BaseController
}

var DefaultUiElementController UiElementController

func (c *UiElementController) List() (*commondto.PaginationRecord[models.UiElement], error) {
	query := dto.UiElementQueryDTO{}
	err := query.FromContext(c.Context(), c.Request)
	if err != nil {
		return nil, err
	}
	items := make([]models.UiElement, 0)
	var total int64
	var totalPages int64
	if *query.IsDirEqual {
		db, err := query.ToGORMQuery(gormx.GetDB(c.MantisContext))
		if err != nil {
			return nil, err
		}
		db = db.Where("is_deleted = ?", false)
		db = db.Where("space_id = ?", c.Request.Header.Get("spaceid"))
		err = db.Model(&models.UiElement{}).Find(&items).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		err = gormx.RawReturnError(c.MantisContext, fmt.Sprintf(`select count(1) from "%s" where is_dir = true and space_id = ? and is_deleted = false`, models.UiElement{}.TableName()), &total, c.Request.Header.Get("spaceid"))
		if err != nil {
			return nil, err
		}
	} else {
		limit := 0
		offset := 0
		if query.PerPage > 0 {
			limit = query.PerPage
			if query.Page > 0 {
				offset = (query.Page - 1) * query.PerPage
			}
		}
		like := "%" + "%"
		if query.NameLike != nil {
			like = "%" + *query.NameLike + "%"
		}
		if *query.ParentIdEqual == "-1" {
			sql := `WITH RECURSIVE descendants AS (
				-- 基础情况：选择指定 parent_id 的节点
				SELECT id, parent_id, name, is_dir, is_deleted, sibling_order, space_id, created, updated, creator, modifier, locator
				FROM ui_element
				WHERE space_id = ? 
				AND parent_id = ''
				AND is_deleted = false

				UNION ALL

				-- 递归情况：查找子节点的子节点
				SELECT u.id, u.parent_id, u.name, u.is_dir, u.is_deleted, u.sibling_order, u.space_id, u.created, u.updated, u.creator, u.modifier, u.locator
				FROM ui_element u
				INNER JOIN descendants d ON u.parent_id = d.id
				WHERE u.is_deleted = false
			)
			SELECT id, parent_id, name, sibling_order, space_id, created, updated, creator, modifier, locator
			FROM descendants
			WHERE is_dir = false
			AND name ILIKE ?
			ORDER BY sibling_order
			LIMIT ? OFFSET ?;`
			err = gormx.RawReturnError(c.MantisContext, sql, &items, c.Request.Header.Get("spaceid"), like, limit, offset)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, err
			}
			countSql := `WITH RECURSIVE descendants AS (
				-- 基础情况：选择指定 parent_id 的节点
				SELECT id, parent_id, name, is_dir, is_deleted, sibling_order, space_id, created, updated, creator, modifier, locator
				FROM ui_element
				WHERE space_id = ? 
				AND parent_id = ''
				AND is_deleted = false

				UNION ALL

				-- 递归情况：查找子节点的子节点
				SELECT u.id, u.parent_id, u.name, u.is_dir, u.is_deleted, u.sibling_order, u.space_id, u.created, u.updated, u.creator, u.modifier, u.locator
				FROM ui_element u
				INNER JOIN descendants d ON u.parent_id = d.id
				WHERE u.is_deleted = false
			)
			SELECT count(1)
			FROM descendants
			WHERE is_dir = false
			AND name ILIKE ?;`
			err = gormx.RawReturnError(c.MantisContext, countSql, &total, c.Request.Header.Get("spaceid"), like)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, err
			}
		} else {
			sql := `WITH RECURSIVE descendants AS (
				-- 基础情况：选择指定 parent_id 的节点
				SELECT id, parent_id, name, is_dir, is_deleted, sibling_order, space_id, created, updated, creator, modifier, locator
				FROM ui_element
				WHERE parent_id = ?
				AND is_deleted = false

				UNION ALL

				-- 递归情况：查找子节点的子节点
				SELECT u.id, u.parent_id, u.name, u.is_dir, u.is_deleted, u.sibling_order, u.space_id, u.created, u.updated, u.creator, u.modifier, u.locator
				FROM ui_element u
				INNER JOIN descendants d ON u.parent_id = d.id
				WHERE u.is_deleted = false
			)
			SELECT id, parent_id, name, sibling_order, space_id, created, updated, creator, modifier, locator
			FROM descendants
			WHERE is_dir = false
			AND name ILIKE ?
			ORDER BY sibling_order
			LIMIT ? OFFSET ?;`
			err = gormx.RawReturnError(c.MantisContext, sql, &items, query.ParentIdEqual, like, limit, offset)
			if err != nil && err != gorm.ErrRecordNotFound {
				return nil, err
			}
			countSql := `WITH RECURSIVE descendants AS (
				-- 基础情况：选择指定 parent_id 的节点
				SELECT id, parent_id, name, is_dir, is_deleted, sibling_order, space_id, created, updated, creator, modifier, locator
				FROM ui_element
				WHERE parent_id = ?
				AND is_deleted = false

				UNION ALL

				-- 递归情况：查找子节点的子节点
				SELECT u.id, u.parent_id, u.name, u.is_dir, u.is_deleted, u.sibling_order, u.space_id, u.created, u.updated, u.creator, u.modifier, u.locator
				FROM ui_element u
				INNER JOIN descendants d ON u.parent_id = d.id
				WHERE u.is_deleted = false
			)
			SELECT count(1)
			FROM descendants
			WHERE is_dir = false
			AND name ILIKE ?;`
			err = gormx.RawReturnError(c.MantisContext, countSql, &total, query.ParentIdEqual, like)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, err
			}
		}
	}
	// Expand fields

	if query.ParentIdExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.ParentId)
		}
		f, ok := expand.GetExpandFunc("ui_element")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["parent_id"] = res[items[i].ParentId]

			}
		}
	}
	totalPages = total / int64(query.PerPage)
	if total%int64(query.PerPage) > 0 {
		totalPages += 1
	}
	return &commondto.PaginationRecord[models.UiElement]{
		Page:       query.Page,
		PerPage:    query.PerPage,
		Items:      items,
		TotalItems: total,
		TotalPages: totalPages,
	}, nil
}

func (c *UiElementController) Get(req *UiElementIdRequest) (*models.UiElement, error) {
	res := &models.UiElement{}
	res.Id = req.Id
	res.IsDeleted = false
	if err := gormx.SelectOneByConditionReturnError(c.MantisContext, res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c *UiElementController) Delete(req *UiElementIdRequest) error {
	model := &models.UiElement{}
	model.Id = req.Id
	model.IsDeleted = true
	_, err := gormx.UpdateOneByConditionReturnError(c.MantisContext, model)
	return err
}

func (c *UiElementController) BatchDelete(req commondto.BatchDeleteReq) error {
	del := &models.UiElement{}
	del.IsDeleted = true
	_, err := gormx.UpdateBatchByParamBuilderReturnError(c.MantisContext, gormx.NewParamBuilder().Model(&models.UiElement{}).In("id", req.Ids), del)
	return err
}

func (c *UiElementController) Create(req *models.UiElement) (*models.UiElement, error) {
	req.SpaceId = c.Request.Header.Get("spaceid")
	req.IsDeleted = false
	err := req.SetCreatorFromReq(c.Request)
	if err != nil {
		return nil, err
	}
	if _, err = gormx.InsertOne(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *UiElementController) Update(req *models.UiElement, idReq *UiElementIdRequest) (*models.UiElement, error) {
	req.Id = idReq.Id
	req.SpaceId = c.Request.Header.Get("spaceid")
	err := req.SetModifierFromReq(c.Request)
	if err != nil {
		return nil, err
	}
	if _, err = gormx.UpdateOneByConditionReturnError(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *UiElementController) Move(req commondto.MoveTreeNodeRequest) error {
	tableName := models.UiElement{}.TableName()
	sql := fmt.Sprintf(`WITH RECURSIVE res AS (
		-- 非递归数据
		SELECT id,name,parent_id FROM "%s" WHERE id=?
		-- 递归
		UNION
		SELECT t2.id,t2.name,t2.parent_id FROM res t1 INNER JOIN "%s" t2 ON t1.parent_id = t2.id
	)
	SELECT id FROM res WHERE (parent_id IS NULL OR parent_id = '') AND name='根节点'`, tableName, tableName)
	for _, node := range req.Nodes {
		if node.NewParentId == "" {
			var res commondto.MoveDto
			// 查询此节点的虚拟父节点
			err := gormx.RawReturnError(c.MantisContext, sql, &res, node.Id)
			if err != nil {
				return err
			}
			node.NewParentId = res.Id
		}
		if err := gormx.Move(c.MantisContext, tableName, node.Id, node.NewParentId, node.NewSiblingId); err != nil {
			return err
		}
	}
	return nil
}
