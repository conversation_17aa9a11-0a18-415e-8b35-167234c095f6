package migration

import (
	"context"
	"embed"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

//go:embed postgres/*.sql
var migrations embed.FS

// Init 初始化数据
func Init() {
	ctx := &commoncontext.MantisContext{Context: context.Background()}
	if !configs.Config.Db.Migrate {
		return
	}
	err := gormx.GetDB(ctx).AutoMigrate(
		&models.DatabaseConfig{},
		&models.DataSource{},
		&models.MockType{},
		&models.Variable{},
	)
	if err != nil {
		return
	}
	_, err = gormx.ExecReturnError(ctx, `TRUNCATE TABLE mock_type;`)
	if err != nil {
		return
	}
	_, err = gormx.ExecReturnError(ctx, `INSERT INTO mock_type ("id", "creator", "modifier", "is_deleted", "type", "name", "command", "description", "examples") VALUES
    ('kX9mPqWnLmJ2vY8', 'system', 'system', 'N', '通用', '随机GUID', '{{_guid}}', '生成一个随机GUID', '[{"example":"{{_guid}}","result":["6ba7b810-9dad-11d1-80b4-00c04fd430c8","123e4567-e89b-12d3-a456-************"]}]'),
    ('aB7mNxQkRtY3wZ2', 'system', 'system', 'N', '通用', '当前时间戳', '{{_timestamp}}', '生成当前时间戳', '[{"example":"{{_timestamp}}","result":["1697059200","1697145600"]}]'),
    ('zT4rHvLmNpQ8xJ6', 'system', 'system', 'N', '通用', '当前ISO时间戳', '{{_isoTimestamp}}', '生成当前ISO格式时间戳', '[{"example":"{{_isoTimestamp}}","result":["2023-10-12T00:00:00Z","2023-10-13T00:00:00Z"]}]'),
    ('yU2wKmJrFxP9tN3', 'system', 'system', 'N', '通用', '随机UUID', '{{_randomUUID}}', '生成一个随机UUID', '[{"example":"{{_randomUUID}}","result":["123e4567-e89b-12d3-a456-************","550e8400-e29b-41d4-a716-************"]}]'),
    ('pR8nQwZkVyT5mL1', 'system', 'system', 'N', '文本、数字和颜色', '随机字母数字', '{{_randomAlphaNumeric}}', '生成一个随机字母数字字符串', '[{"example":"{{_randomAlphaNumeric}}","result":["aB9xY2","kM7pQ8"]}]'),
    ('qW3tYjXnLmB6vK9', 'system', 'system', 'N', '文本、数字和颜色', '随机布尔值', '{{_randomBoolean}}', '生成一个随机布尔值', '[{"example":"{{_randomBoolean}}","result":["FALSE","TRUE","TRUE"]}]'),
    ('xJ5kRtHmPqN2zF7', 'system', 'system', 'N', '文本、数字和颜色', '随机整数', '{{_randomInt}}', '生成一个随机整数', '[{"example":"{{_randomInt}}","result":["42","123","789"]}]'),
    ('mL9vQwZkRtY4nP8', 'system', 'system', 'N', '文本、数字和颜色', '随机颜色', '{{_randomColor}}', '生成一个随机安全颜色名称', '[{"example":"{{_randomColor}}","result":["Red","Blue"]}]'),
    ('tN2xJmKrVyP6wQ3', 'system', 'system', 'N', '文本、数字和颜色', '随机十六进制颜色', '{{_randomHexColor}}', '生成一个随机十六进制颜色', '[{"example":"{{_randomHexColor}}","result":["#FF5733","#00FF00"]}]'),
    ('vK7mPqWnLxT3zR5', 'system', 'system', 'N', '文本、数字和颜色', '随机缩写', '{{_randomAbbreviation}}', '生成一个随机缩写', '[{"example":"{{_randomAbbreviation}}","result":["CEO","CFO"]}]'),
    ('bF4rHvLmNpQ9xJ2', 'system', 'system', 'N', '网络和IP地址', '随机IP地址', '{{_randomIP}}', '生成一个随机IPv4地址', '[{"example":"{{_randomIP}}","result":["***********","********"]}]'),
    ('nP8tYjXnLmB5vK1', 'system', 'system', 'N', '网络和IP地址', '随机IPv6地址', '{{_randomIPV6}}', '生成一个随机IPv6地址', '[{"example":"{{_randomIPV6}}","result":["2001:0db8:85a3:0000:0000:8a2e:0370:7334"]}]'),
    ('wQ3kRtHmPqN6zF9', 'system', 'system', 'N', '网络和IP地址', '随机MAC地址', '{{_randomMACAddress}}', '生成一个随机MAC地址', '[{"example":"{{_randomMACAddress}}","result":["00:1A:2B:3C:4D:5E","AA:BB:CC:DD:EE:FF"]}]'),
    ('zR5mPqWnLxT7vK4', 'system', 'system', 'N', '网络和IP地址', '随机密码', '{{_randomPassword}}', '生成一个随机密码', '[{"example":"{{_randomPassword}}","result":["Xyz123!@#","Secure789"]}]'),
    ('jN2xJmKrVyP8wQ6', 'system', 'system', 'N', '网络和IP地址', '随机语言环境', '{{_randomLocale}}', '生成一个随机语言环境', '[{"example":"{{_randomLocale}}","result":["en_US","zh_CN"]}]'),
    ('kM9vQwZkRtY2nP7', 'system', 'system', 'N', '网络和IP地址', '随机用户代理', '{{_randomUserAgent}}', '生成一个随机用户代理字符串', '[{"example":"{{_randomUserAgent}}","result":["Mozilla/5.0 (Windows NT 10.0; Win64; x64)","Chrome/91.0.4472.124"]}]'),
    ('tF4rHvLmNpQ1xJ3', 'system', 'system', 'N', '网络和IP地址', '随机协议', '{{_randomProtocol}}', '生成一个随机协议', '[{"example":"{{_randomProtocol}}","result":["http","https"]}]'),
    ('pW8tYjXnLmB9vK5', 'system', 'system', 'N', '网络和IP地址', '随机语义支持版本', '{{_randomSemver}}', '生成一个随机语义版本号', '[{"example":"{{_randomSemver}}","result":["1.0.0","2.3.4"]}]'),
    ('xQ3kRtHmPqN4zF2', 'system', 'system', 'N', '名称', '随机名字', '{{_randomFirstName}}', '生成一个随机名字', '[{"example":"{{_randomFirstName}}","result":["John","Emma"]}]'),
    ('mR5mPqWnLxT6vK8', 'system', 'system', 'N', '名称', '随机姓氏', '{{_randomLastName}}', '生成一个随机姓氏', '[{"example":"{{_randomLastName}}","result":["Smith","Johnson"]}]'),
    ('yN2xJmKrVyP7wQ1', 'system', 'system', 'N', '名称', '随机全名', '{{_randomFullName}}', '生成一个随机全名', '[{"example":"{{_randomFullName}}","result":["John Smith","Emma Johnson"]}]'),
    ('vK9vQwZkRtY3nP6', 'system', 'system', 'N', '名称', '随机名称前缀', '{{_randomNamePrefix}}', '生成一个随机名称前缀', '[{"example":"{{_randomNamePrefix}}","result":["Mr.","Ms."]}]'),
    ('bF7rHvLmNpQ2xJ4', 'system', 'system', 'N', '名称', '随机名称后缀', '{{_randomNameSuffix}}', '生成一个随机名称后缀', '[{"example":"{{_randomNameSuffix}}","result":["Jr.","Sr."]}]'),
    ('nP1tYjXnLmB8vK3', 'system', 'system', 'N', '职业', '随机工作领域', '{{_randomJobArea}}', '生成一个随机工作领域', '[{"example":"{{_randomJobArea}}","result":["Engineering","Marketing"]}]'),
    ('wQ6kRtHmPqN5zF9', 'system', 'system', 'N', '职业', '随机职位描述', '{{_randomJobDescriptor}}', '生成一个随机职位描述', '[{"example":"{{_randomJobDescriptor}}","result":["Senior","Lead"]}]'),
    ('zR4mPqWnLxT9vK7', 'system', 'system', 'N', '职业', '随机职位名称', '{{_randomJobTitle}}', '生成一个随机职位名称', '[{"example":"{{_randomJobTitle}}","result":["Software Engineer","Product Manager"]}]'),
    ('jN8xJmKrVyP1wQ5', 'system', 'system', 'N', '职业', '随机工作类型', '{{_randomJobType}}', '生成一个随机工作类型', '[{"example":"{{_randomJobType}}","result":["Full-time","Contract"]}]'),
    ('kM2vQwZkRtY7nP4', 'system', 'system', 'N', '电话、地址和位置', '随机电话号码', '{{_randomPhoneNumber}}', '生成一个随机电话号码', '[{"example":"{{_randomPhoneNumber}}","result":["************","************"]}]'),
    ('tF9rHvLmNpQ3xJ6', 'system', 'system', 'N', '电话、地址和位置', '随机扩展电话号码', '{{_randomPhoneNumberExt}}', '生成一个随机扩展电话号码', '[{"example":"{{_randomPhoneNumberExt}}","result":["88-************"]}]'),
    ('pW7tYjXnLmB2vK8', 'system', 'system', 'N', '电话、地址和位置', '随机城市', '{{_randomCity}}', '生成一个随机城市名称', '[{"example":"{{_randomCity}}","result":["New York","Tokyo"]}]'),
    ('xQ5kRtHmPqN9zF1', 'system', 'system', 'N', '电话、地址和位置', '随机街道名称', '{{_randomStreetName}}', '生成一个随机街道名称', '[{"example":"{{_randomStreetName}}","result":["Main St","Oak Ave"]}]'),
    ('mR3mPqWnLxT4vK6', 'system', 'system', 'N', '电话、地址和位置', '随机街道地址', '{{_randomStreetAddress}}', '生成一个随机街道地址', '[{"example":"{{_randomStreetAddress}}","result":["123 Main St","456 Oak Ave"]}]'),
    ('yN1xJmKrVyP8wQ2', 'system', 'system', 'N', '电话、地址和位置', '随机国家', '{{_randomCountry}}', '生成一个随机国家名称', '[{"example":"{{_randomCountry}}","result":["USA","Japan"]}]'),
    ('vK6vQwZkRtY5nP9', 'system', 'system', 'N', '电话、地址和位置', '随机国家代码', '{{_randomCountryCode}}', '生成一个随机国家代码', '[{"example":"{{_randomCountryCode}}","result":["US","JP"]}]'),
    ('bF8rHvLmNpQ7xJ3', 'system', 'system', 'N', '电话、地址和位置', '随机纬度', '{{_randomLatitude}}', '生成一个随机纬度', '[{"example":"{{_randomLatitude}}","result":["40.7128","51.5074"]}]'),
    ('nP2tYjXnLmB1vK5', 'system', 'system', 'N', '电话、地址和位置', '随机经度', '{{_randomLongitude}}', '生成一个随机经度', '[{"example":"{{_randomLongitude}}","result":["-74.0060","0.1278"]}]'),
    ('wQ9kRtHmPqN3zF7', 'system', 'system', 'N', '图片', '随机头像图片', '{{_randomAvatarImage}}', '生成一个随机头像图片URL', '[{"example":"{{_randomAvatarImage}}","result":["https://example.com/avatar.jpg","https://test.com/avatar.png"]}]'),
    ('zR7mPqWnLxT2vK4', 'system', 'system', 'N', '图片', '随机图片URL', '{{_randomImageUrl}}', '生成一个随机图片URL', '[{"example":"{{_randomImageUrl}}","result":["https://example.com/image.jpg","https://test.com/photo.png"]}]'),
    ('jN5xJmKrVyP6wQ8', 'system', 'system', 'N', '图片', '随机抽象图片', '{{_randomAbstractImage}}', '生成一个随机抽象图片URL', '[{"example":"{{_randomAbstractImage}}","result":["https://example.com/abstract.jpg","https://test.com/abstract.png"]}]'),
    ('kM3vQwZkRtY9nP1', 'system', 'system', 'N', '图片', '随机动物图片', '{{_randomAnimalsImage}}', '生成一个随机动物图片URL', '[{"example":"{{_randomAnimalsImage}}","result":["https://example.com/animal.jpg","https://test.com/animal.png"]}]'),
    ('tF1rHvLmNpQ4xJ6', 'system', 'system', 'N', '图片', '随机商业图片', '{{_randomBusinessImage}}', '生成一个随机商业图片URL', '[{"example":"{{_randomBusinessImage}}","result":["https://example.com/business.jpg","https://test.com/business.png"]}]'),
    ('pW6tYjXnLmB8vK2', 'system', 'system', 'N', '图片', '随机猫咪图片', '{{_randomCatsImage}}', '生成一个随机猫咪图片URL', '[{"example":"{{_randomCatsImage}}","result":["https://example.com/cat.jpg","https://test.com/cat.png"]}]'),
    ('xQ4kRtHmPqN7zF9', 'system', 'system', 'N', '图片', '随机城市图片', '{{_randomCityImage}}', '生成一个随机城市图片URL', '[{"example":"{{_randomCityImage}}","result":["https://example.com/city.jpg","https://test.com/city.png"]}]'),
    ('mR2mPqWnLxT5vK3', 'system', 'system', 'N', '图片', '随机美食图片', '{{_randomFoodImage}}', '生成一个随机美食图片URL', '[{"example":"{{_randomFoodImage}}","result":["https://example.com/food.jpg","https://test.com/food.png"]}]'),
    ('yN9xJmKrVyP1wQ7', 'system', 'system', 'N', '图片', '随机夜生活图片', '{{_randomNightlifeImage}}', '生成一个随机夜生活图片URL', '[{"example":"{{_randomNightlifeImage}}","result":["https://example.com/nightlife.jpg","https://test.com/nightlife.png"]}]'),
    ('vK8vQwZkRtY3nP5', 'system', 'system', 'N', '图片', '随机时尚图片', '{{_randomFashionImage}}', '生成一个随机时尚图片URL', '[{"example":"{{_randomFashionImage}}","result":["https://example.com/fashion.jpg","https://test.com/fashion.png"]}]'),
    ('bF6rHvLmNpQ2xJ4', 'system', 'system', 'N', '图片', '随机人物图片', '{{_randomPeopleImage}}', '生成一个随机人物图片URL', '[{"example":"{{_randomPeopleImage}}","result":["https://example.com/people.jpg","https://test.com/people.png"]}]'),
    ('nP3tYjXnLmB9vK1', 'system', 'system', 'N', '图片', '随机自然图片', '{{_randomNatureImage}}', '生成一个随机自然图片URL', '[{"example":"{{_randomNatureImage}}","result":["https://example.com/nature.jpg","https://test.com/nature.png"]}]'),
    ('wQ7kRtHmPqN4zF8', 'system', 'system', 'N', '图片', '随机运动图片', '{{_randomSportsImage}}', '生成一个随机运动图片URL', '[{"example":"{{_randomSportsImage}}","result":["https://example.com/sports.jpg","https://test.com/sports.png"]}]'),
    ('zR5mPqWnLxT6vK2', 'system', 'system', 'N', '图片', '随机交通图片', '{{_randomTransportImage}}', '生成一个随机交通图片URL', '[{"example":"{{_randomTransportImage}}","result":["https://example.com/transport.jpg","https://test.com/transport.png"]}]'),
    ('jN4xJmKrVyP8wQ3', 'system', 'system', 'N', '图片', '随机图片数据URI', '{{_randomImageDataUri}}', '生成一个随机图片数据URI', '[{"example":"{{_randomImageDataUri}}","result":["data:image/png;base64,iVBORw0KGgo=","data:image/jpeg;base64,/9j/4AAQSkZJRg=="]}]'),
    ('kM1vQwZkRtY7nP9', 'system', 'system', 'N', '金融', '随机银行账户', '{{_randomBankAccount}}', '生成一个随机银行账户号码', '[{"example":"{{_randomBankAccount}}","result":["**********","**********"]}]'),
    ('tF8rHvLmNpQ5xJ2', 'system', 'system', 'N', '金融', '随机银行账户名称', '{{_randomBankAccountName}}', '生成一个随机银行账户名称', '[{"example":"{{_randomBankAccountName}}","result":["Savings Account","Checking Account"]}]'),
    ('pW5tYjXnLmB3vK6', 'system', 'system', 'N', '金融', '随机信用卡掩码', '{{_randomCreditCardMask}}', '生成一个随机信用卡掩码', '[{"example":"{{_randomCreditCardMask}}","result":["****-****-****-1234","****-****-****-5678"]}]'),
    ('xQ2kRtHmPqN9zF4', 'system', 'system', 'N', '金融', '随机银行账户BIC', '{{_randomBankAccountBic}}', '生成一个随机银行BIC代码', '[{"example":"{{_randomBankAccountBic}}","result":["BOFAUS3NXXX","NWBKGB2LXXX"]}]'),
    ('mR9mPqWnLxT1vK7', 'system', 'system', 'N', '金融', '随机银行账户IBAN', '{{_randomBankAccountIban}}', '生成一个随机IBAN号码', '[{"example":"{{_randomBankAccountIban}}","result":["**********************","**********************"]}]'),
    ('yN6xJmKrVyP4wQ8', 'system', 'system', 'N', '金融', '随机交易类型', '{{_randomTransactionType}}', '生成一个随机交易类型', '[{"example":"{{_randomTransactionType}}","result":["Deposit","Withdrawal"]}]'),
    ('vK3vQwZkRtY2nP5', 'system', 'system', 'N', '金融', '随机货币代码', '{{_randomCurrencyCode}}', '生成一个随机货币代码', '[{"example":"{{_randomCurrencyCode}}","result":["USD","EUR"]}]'),
    ('bF1rHvLmNpQ8xJ9', 'system', 'system', 'N', '金融', '随机货币名称', '{{_randomCurrencyName}}', '生成一个随机货币名称', '[{"example":"{{_randomCurrencyName}}","result":["US Dollar","Euro"]}]'),
    ('nP7tYjXnLmB4vK2', 'system', 'system', 'N', '金融', '随机货币符号', '{{_randomCurrencySymbol}}', '生成一个随机货币符号', '[{"example":"{{_randomCurrencySymbol}}","result":["$","€"]}]'),
    ('wQ4kRtHmPqN6zF3', 'system', 'system', 'N', '金融', '随机比特币地址', '{{_randomBitcoin}}', '生成一个随机比特币地址', '[{"example":"{{_randomBitcoin}}","result":["1A1zP1eP5QGefi2DMPTf","3J98t1WpEZ73CNmQviecr"]}]'),
    ('zR2mPqWnLxT9vK1', 'system', 'system', 'N', '商业', '随机公司名称', '{{_randomCompanyName}}', '生成一个随机公司名称', '[{"example":"{{_randomCompanyName}}","result":["Acme Corp","Globex Inc"]}]'),
    ('jN8xJmKrVyP5wQ7', 'system', 'system', 'N', '商业', '随机公司后缀', '{{_randomCompanySuffix}}', '生成一个随机公司后缀', '[{"example":"{{_randomCompanySuffix}}","result":["Inc","LLC"]}]'),
    ('kM6vQwZkRtY3nP4', 'system', 'system', 'N', '商业', '随机商业术语', '{{_randomBs}}', '生成一个随机商业术语', '[{"example":"{{_randomBs}}","result":["Synergy","Paradigm"]}]'),
    ('tF4rHvLmNpQ1xJ8', 'system', 'system', 'N', '商业', '随机商业形容词', '{{_randomBsAdjective}}', '生成一个随机商业形容词', '[{"example":"{{_randomBsAdjective}}","result":["Cutting-edge","Innovative"]}]'),
    ('pW9tYjXnLmB7vK5', 'system', 'system', 'N', '商业', '随机商业热词', '{{_randomBsBuzz}}', '生成一个随机商业热词', '[{"example":"{{_randomBsBuzz}}","result":["Leverage","Disrupt"]}]'),
    ('xQ6kRtHmPqN2zF3', 'system', 'system', 'N', '商业', '随机商业名词', '{{_randomBsNoun}}', '生成一个随机商业名词', '[{"example":"{{_randomBsNoun}}","result":["Solution","Platform"]}]'),
    ('mR4mPqWnLxT8vK9', 'system', 'system', 'N', '标语', '随机标语', '{{_randomCatchPhrase}}', '生成一个随机标语', '[{"example":"{{_randomCatchPhrase}}","result":["Think Different","Just Do It"]}]'),
    ('yN2xJmKrVyP6wQ1', 'system', 'system', 'N', '标语', '随机标语形容词', '{{_randomCatchPhraseAdjective}}', '生成一个随机标语形容词', '[{"example":"{{_randomCatchPhraseAdjective}}","result":["Bold","Innovative"]}]'),
    ('vK7vQwZkRtY4nP3', 'system', 'system', 'N', '标语', '随机标语描述', '{{_randomCatchPhraseDescriptor}}', '生成一个随机标语描述', '[{"example":"{{_randomCatchPhraseDescriptor}}","result":["Vision","Future"]}]'),
    ('bF5rHvLmNpQ9xJ2', 'system', 'system', 'N', '标语', '随机标语名词', '{{_randomCatchPhraseNoun}}', '生成一个随机标语名词', '[{"example":"{{_randomCatchPhraseNoun}}","result":["Success","Innovation"]}]'),
    ('nP3tYjXnLmB1vK6', 'system', 'system', 'N', '数据库', '随机数据库列', '{{_randomDatabaseColumn}}', '生成一个随机数据库列名', '[{"example":"{{_randomDatabaseColumn}}","result":["user_id","created_at"]}]'),
    ('wQ8kRtHmPqN5zF4', 'system', 'system', 'N', '数据库', '随机数据库类型', '{{_randomDatabaseType}}', '生成一个随机数据库类型', '[{"example":"{{_randomDatabaseType}}","result":["varchar","int"]}]'),
    ('zR6mPqWnLxT2vK8', 'system', 'system', 'N', '数据库', '随机数据库排序规则', '{{_randomDatabaseCollation}}', '生成一个随机数据库排序规则', '[{"example":"{{_randomDatabaseCollation}}","result":["utf8_general_ci","latin1_swedish_ci"]}]'),
    ('jN4xJmKrVyP7wQ9', 'system', 'system', 'N', '数据库', '随机数据库引擎', '{{_randomDatabaseEngine}}', '生成一个随机数据库引擎', '[{"example":"{{_randomDatabaseEngine}}","result":["InnoDB","MyISAM"]}]'),
    ('kM2vQwZkRtY8nP5', 'system', 'system', 'N', '日期', '随机未来日期', '{{_randomDateFuture}}', '生成一个随机未来日期', '[{"example":"{{_randomDateFuture}}","result":["2025-05-15","2026-12-31"]}]'),
    ('tF9rHvLmNpQ3xJ1', 'system', 'system', 'N', '日期', '随机过去日期', '{{_randomDatePast}}', '生成一个随机过去日期', '[{"example":"{{_randomDatePast}}","result":["2022-05-15","2021-12-31"]}]'),
    ('pW7tYjXnLmB4vK2', 'system', 'system', 'N', '日期', '随机近期日期', '{{_randomDateRecent}}', '生成一个随机近期日期', '[{"example":"{{_randomDateRecent}}","result":["2025-04-20","2025-04-23"]}]'),
    ('xQ5kRtHmPqN9zF6', 'system', 'system', 'N', '日期', '随机工作日', '{{_randomWeekday}}', '生成一个随机工作日', '[{"example":"{{_randomWeekday}}","result":["Monday","Friday"]}]'),
    ('mR3mPqWnLxT1vK7', 'system', 'system', 'N', '日期', '随机月份', '{{_randomMonth}}', '生成一个随机月份', '[{"example":"{{_randomMonth}}","result":["January","December"]}]'),
    ('yN8xJmKrVyP6wQ4', 'system', 'system', 'N', '域名、邮箱和用户名', '随机域名', '{{_randomDomainName}}', '生成一个随机域名', '[{"example":"{{_randomDomainName}}","result":["example.com","test.org"]}]'),
    ('vK6vQwZkRtY2nP9', 'system', 'system', 'N', '域名、邮箱和用户名', '随机域名后缀', '{{_randomDomainSuffix}}', '生成一个随机域名后缀', '[{"example":"{{_randomDomainSuffix}}","result":["com","org"]}]'),
    ('bF4rHvLmNpQ7xJ5', 'system', 'system', 'N', '域名、邮箱和用户名', '随机域名单词', '{{_randomDomainWord}}', '生成一个随机域名单词', '[{"example":"{{_randomDomainWord}}","result":["example","test"]}]'),
    ('nP2tYjXnLmB8vK3', 'system', 'system', 'N', '域名、邮箱和用户名', '随机邮箱', '{{_randomEmail}}', '生成一个随机邮箱地址', '[{"example":"{{_randomEmail}}","result":["<EMAIL>","<EMAIL>"]}]'),
    ('wQ9kRtHmPqN4zF1', 'system', 'system', 'N', '域名、邮箱和用户名', '随机示例邮箱', '{{_randomExampleEmail}}', '生成一个随机示例邮箱地址', '[{"example":"{{_randomExampleEmail}}","result":["<EMAIL>","<EMAIL>"]}]'),
    ('zR7mPqWnLxT5vK2', 'system', 'system', 'N', '域名、邮箱和用户名', '随机用户名', '{{_randomUserName}}', '生成一个随机用户名', '[{"example":"{{_randomUserName}}","result":["john_doe","user456"]}]'),
    ('jN5xJmKrVyP9wQ6', 'system', 'system', 'N', '域名、邮箱和用户名', '随机URL', '{{_randomUrl}}', '生成一个随机URL', '[{"example":"{{_randomUrl}}","result":["https://example.com","http://test.org"]}]'),
    ('kM3vQwZkRtY1nP8', 'system', 'system', 'N', '文件和目录', '随机文件名', '{{_randomFileName}}', '生成一个随机文件名', '[{"example":"{{_randomFileName}}","result":["document.pdf","image.jpg"]}]'),
    ('tF1rRrHvLmNpQ6xJ4', 'system', 'system', 'N', '文件和目录', '随机文件类型', '{{_randomFileType}}', '生成一个随机文件类型', '[{"example":"{{_randomFileType}}","result":["pdf","jpg"]}]'),
    ('pW8tYjXnLmB2vK9', 'system', 'system', 'N', '文件和目录', '随机文件扩展名', '{{_randomFileExt}}', '生成一个随机文件扩展名', '[{"example":"{{_randomFileExt}}","result":[".pdf",".jpg"]}]'),
    ('xQ6kRtHmPqN7zF5', 'system', 'system', 'N', '文件和目录', '随机常用文件名', '{{_randomCommonFileName}}', '生成一个随机常用文件名', '[{"example":"{{_randomCommonFileName}}","result":["readme.txt","index.html"]}]'),
    ('mR4mPqWnLxT3vK1', 'system', 'system', 'N', '文件和目录', '随机常用文件类型', '{{_randomCommonFileType}}', '生成一个随机常用文件类型', '[{"example":"{{_randomCommonFileType}}","result":["txt","html"]}]'),
    ('yN2xJmKrVyP8wQ7', 'system', 'system', 'N', '文件和目录', '随机常用文件扩展名', '{{_randomCommonFileExt}}', '生成一个随机常用文件扩展名', '[{"example":"{{_randomCommonFileExt}}","result":[".txt",".html"]}]'),
    ('vK9vQwZkRtY4nP6', 'system', 'system', 'N', '文件和目录', '随机文件路径', '{{_randomFilePath}}', '生成一个随机文件路径', '[{"example":"{{_randomFilePath}}","result":["path/to/file.txt","/docs/image.jpg"]}]'),
    ('bF7rHvLmNpQ1xJ8', 'system', 'system', 'N', '文件和目录', '随机目录路径', '{{_randomDirectoryPath}}', '生成一个随机目录路径', '[{"example":"{{_randomDirectoryPath}}","result":["path/to/dir","/docs/folder"]}]'),
    ('nP5tYjXnLmB9vK4', 'system', 'system', 'N', '文件和目录', '随机MIME类型', '{{_randomMimeType}}', '生成一个随机MIME类型', '[{"example":"{{_randomMimeType}}","result":["application/pdf","image/jpeg"]}]'),
    ('wQ3kRtHmPqN6zF2', 'system', 'system', 'N', '商店', '随机价格', '{{_randomPrice}}', '生成一个随机价格', '[{"example":"{{_randomPrice}}","result":["99.99","49.50"]}]'),
    ('zR1mPqWnLxT4vK9', 'system', 'system', 'N', '商店', '随机产品', '{{_randomProduct}}', '生成一个随机产品', '[{"example":"{{_randomProduct}}","result":["Laptop","Phone"]}]'),
    ('jN8xJmKrVyP2wQ5', 'system', 'system', 'N', '商店', '随机产品形容词', '{{_randomProductAdjective}}', '生成一个随机产品形容词', '[{"example":"{{_randomProductAdjective}}","result":["Sleek","Durable"]}]'),
    ('kM6vQwZkRtY7nP3', 'system', 'system', 'N', '商店', '随机产品材料', '{{_randomProductMaterial}}', '生成一个随机产品材料', '[{"example":"{{_randomProductMaterial}}","result":["Leather","Plastic"]}]'),
    ('tF4rHvLmNpQ8xJ1', 'system', 'system', 'N', '商店', '随机产品名称', '{{_randomProductName}}', '生成一个随机产品名称', '[{"example":"{{_randomProductName}}","result":["Ultra Widget","Smart Gadget"]}]'),
    ('pW2tYjXnLmB5vK7', 'system', 'system', 'N', '商店', '随机部门', '{{_randomDepartment}}', '生成一个随机部门名称', '[{"example":"{{_randomDepartment}}","result":["Electronics","Clothing"]}]'),
    ('xQ9kRtHmPqN3zF6', 'system', 'system', 'N', '语法', '随机名词', '{{_randomNoun}}', '生成一个随机名词', '[{"example":"{{_randomNoun}}","result":["Table","Car"]}]'),
    ('mR7mPqWnLxT1vK4', 'system', 'system', 'N', '语法', '随机动词', '{{_randomVerb}}', '生成一个随机动词', '[{"example":"{{_randomVerb}}","result":["Run","Jump"]}]'),
    ('yN5xJmKrVyP9wQ2', 'system', 'system', 'N', '语法', '随机进行时动词', '{{_randomIngverb}}', '生成一个随机进行时动词', '[{"example":"{{_randomIngverb}}","result":["Running","Jumping"]}]'),
    ('vK3vQwZkRtY6nP8', 'system', 'system', 'N', '语法', '随机形容词', '{{_randomAdjective}}', '生成一个随机形容词', '[{"example":"{{_randomAdjective}}","result":["Quick","Bright"]}]'),
    ('bF1rHvLmNpQ4xJ9', 'system', 'system', 'N', '语法', '随机单词', '{{_randomWord}}', '生成一个随机单词', '[{"example":"{{_randomWord}}","result":["Apple","Book"]}]'),
    ('nP8tYjXnLmB2vK5', 'system', 'system', 'N', '语法', '随机单词列表', '{{_randomWords}}', '生成一个随机单词列表', '[{"example":"{{_randomWords}}","result":["Apple Book","Car Tree"]}]'),
    ('wQ6kRtHmPqN7zF3', 'system', 'system', 'N', '语法', '随机短语', '{{_randomPhrase}}', '生成一个随机短语', '[{"example":"{{_randomPhrase}}","result":["Break a leg","Piece of cake"]}]'),
    ('zR4mPqWnLxT5vK1', 'system', 'system', 'N', 'Lorem文本', '随机Lorem单词', '{{_randomLoremWord}}', '生成一个随机Lorem单词', '[{"example":"{{_randomLoremWord}}","result":["Lorem","Ipsum"]}]'),
    ('jN2xJmKrVyP3wQ9', 'system', 'system', 'N', 'Lorem文本', '随机Lorem单词列表', '{{_randomLoremWords}}', '生成一个随机Lorem单词列表', '[{"example":"{{_randomLoremWords}}","result":["Lorem Ipsum","Dolor Sit"]}]'),
    ('kM9vQwZkRtY1nP7', 'system', 'system', 'N', 'Lorem文本', '随机Lorem句子', '{{_randomLoremSentence}}', '生成一个随机Lorem句子', '[{"example":"{{_randomLoremSentence}}","result":["Lorem ipsum dolor sit amet.","Consectetur adipiscing elit."]}]'),
    ('tF7rHvLmNpQ8xJ5', 'system', 'system', 'N', 'Lorem文本', '随机Lorem句子列表', '{{_randomLoremSentences}}', '生成一个随机Lorem句子列表', '[{"example":"{{_randomLoremSentences}}","result":["Lorem ipsum dolor.","Sit amet consectetur."]}]'),
    ('pW5tYjXnLmB4vK3', 'system', 'system', 'N', 'Lorem文本', '随机Lorem段落', '{{_randomLoremParagraph}}', '生成一个随机Lorem段落', '[{"example":"{{_randomLoremParagraph}}","result":["Lorem ipsum dolor sit amet..."]}]'),
    ('xQ3kRtHmPqN9zF1', 'system', 'system', 'N', 'Lorem文本', '随机Lorem段落列表', '{{_randomLoremParagraphs}}', '生成一个随机Lorem段落列表', '[{"example":"{{_randomLoremParagraphs}}","result":["Lorem ipsum dolor...","Consectetur adipiscing..."]}]'),
    ('mR1mPqWnLxT6vK8', 'system', 'system', 'N', 'Lorem文本', '随机Lorem文本', '{{_randomLoremText}}', '生成随机Lorem文本', '[{"example":"{{_randomLoremText}}","result":["Lorem ipsum dolor sit amet..."]}]'),
    ('yN8xJmKrVyP4wQ6', 'system', 'system', 'N', 'Lorem文本', '随机Lorem短语', '{{_randomLoremSlug}}', '生成一个随机Lorem短语', '[{"example":"{{_randomLoremSlug}}","result":["lorem-ipsum","dolor-sit"]}]'),
    ('vK6vQwZkRtY2nP4', 'system', 'system', 'N', 'Lorem文本', '随机Lorem行', '{{_randomLoremLines}}', '生成随机Lorem行', '[{"example":"{{_randomLoremLines}}","result":["Lorem ipsum\nDolor sit","Amet consectetur\nAdipiscing elit"]}]'),
    ('bF4rHvLmNpQ7xJ2', 'system', 'system', 'N', '特定', '随机浮点数', '{{_randomFloat}}', '生成一个随机浮点数', '[{"example":"{{_randomFloat}}","result":["3.14","42.567","0.123"]}]'),
    ('nP2tYjXnLmB5vK9', 'system', 'system', 'N', '特定', '随机字符串', '{{_randomString}}', '生成一个随机字符串', '[{"example":"{{_randomString}}","result":["abc123","xyz789","qwerty"]}]');`)
	if err != nil {
		return
	}
}
